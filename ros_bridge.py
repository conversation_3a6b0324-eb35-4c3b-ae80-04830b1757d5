import json
import logging
import websocket
import threading
import time
import signal
import sys
from movement_controller import MovementController

logger = logging.getLogger(__name__)

class ROSBridgePublisher:
    """ROSBridge发布器"""
    
    def __init__(self, ros_bridge_url="ws://192.168.59.173:9090"):
        self.ros_bridge_url = ros_bridge_url
        self.ws = None
        self.connected = False
        self.reconnect_thread = None
        self.reconnect_event = threading.Event()
        self.publish_thread = None
        self.publish_event = threading.Event()
        self.current_cmd_vel = {  # 当前的速度命令
            "linear_x": 0.0,
            "linear_y": 0.0,
            "linear_z": 0.0,
            "angular_x": 0.0,
            "angular_y": 0.0,
            "angular_z": 0.0
        }
        self.publish_interval = 0.1  # 发布间隔（秒）
        
        # 初始化运动控制器
        self.movement_controller = MovementController(self)
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """处理信号"""
        logger.info(f"收到信号 {signum}，正在关闭...")
        self.disconnect()
        sys.exit(0)
    
    def connect(self):
        """连接到ROSBridge服务器"""
        try:
            # 如果已有连接，先断开
            if self.ws:
                self.disconnect()
            
            logger.info(f"正在连接到 {self.ros_bridge_url}...")
            self.ws = websocket.WebSocketApp(
                self.ros_bridge_url,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close,
                on_open=self._on_open
            )
            
            # 启动WebSocket连接线程
            self.ws_thread = threading.Thread(target=self.ws.run_forever)
            self.ws_thread.daemon = True
            self.ws_thread.start()
            
            # 等待连接建立
            for _ in range(10):  # 最多等待10秒
                if self.connected:
                    logger.info("已成功连接到ROSBridge服务器")
                    return True
                time.sleep(1)
            
            logger.error("连接超时")
            return False
            
        except Exception as e:
            logger.error(f"连接失败: {str(e)}")
            return False
    
    def disconnect(self):
        """断开与ROSBridge服务器的连接"""
        logger.info("正在断开连接...")
        self.reconnect_event.set()
        self.publish_event.set()
        
        if self.reconnect_thread and self.reconnect_thread.is_alive():
            self.reconnect_thread.join(timeout=1.0)
        
        if self.publish_thread and self.publish_thread.is_alive():
            self.publish_thread.join(timeout=1.0)
        
        if self.ws:
            self.ws.close()
            self.ws = None
        
        self.connected = False
        logger.info("已断开与ROSBridge服务器的连接")
    
    def _on_message(self, ws, message):
        """WebSocket消息回调"""
        try:
            data = json.loads(message)
            logger.debug(f"收到ROS消息: {data}")
        except Exception as e:
            logger.error(f"处理消息时发生错误: {str(e)}")
    
    def _on_error(self, ws, error):
        """WebSocket错误回调"""
        logger.error(f"WebSocket错误: {str(error)}")
        self.connected = False
        self.ws = None
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket关闭回调"""
        logger.info(f"WebSocket连接已关闭: {close_status_code} - {close_msg}")
        self.connected = False
        self.ws = None
        self._start_reconnect()
    
    def _on_open(self, ws):
        """WebSocket打开回调"""
        logger.info("WebSocket连接已建立")
        self.connected = True
        self.reconnect_event.clear()
        self._advertise_cmd_vel()
    
    def _start_reconnect(self):
        """启动重连线程"""
        if not self.reconnect_thread or not self.reconnect_thread.is_alive():
            self.reconnect_thread = threading.Thread(target=self._reconnect_task)
            self.reconnect_thread.daemon = True
            self.reconnect_event.clear()
            self.reconnect_thread.start()
    
    def _reconnect_task(self):
        """重连任务"""
        while not self.reconnect_event.is_set():
            if not self.connected:
                logger.info("尝试重新连接...")
                if self.connect():
                    logger.info("重连成功")
                    break
            time.sleep(5)  # 每5秒尝试一次重连
    
    def _advertise_cmd_vel(self):
        """向ROS系统广告cmd_vel话题"""
        if not self.connected:
            logger.warning("未连接到ROS，无法广告话题")
            return
        
        try:
            advertise_msg = {
                "op": "advertise",
                "topic": "/cmd_vel",
                "type": "geometry_msgs/Twist"
            }
            self.ws.send(json.dumps(advertise_msg))
            logger.info("已广告cmd_vel话题")
        except Exception as e:
            logger.error(f"广告话题时发生错误: {str(e)}")
    
    def start_publish_loop(self):
        """启动轮询发布线程"""
        if self.publish_thread and self.publish_thread.is_alive():
            return
        
        self.publish_event.clear()
        self.publish_thread = threading.Thread(target=self._publish_loop)
        self.publish_thread.daemon = True
        self.publish_thread.start()
        logger.info("已启动轮询发布线程")
    
    def stop_publish_loop(self):
        """停止轮询发布线程"""
        if self.publish_thread and self.publish_thread.is_alive():
            self.publish_event.set()
            self.publish_thread.join(timeout=1.0)
            logger.info("已停止轮询发布线程")
    
    def _publish_loop(self):
        """轮询发布循环"""
        while not self.publish_event.is_set() and self.connected:
            try:
                # 从当前命令中获取速度值
                linear_x = self.current_cmd_vel["linear_x"]
                linear_y = self.current_cmd_vel["linear_y"]
                linear_z = self.current_cmd_vel["linear_z"]
                angular_x = self.current_cmd_vel["angular_x"]
                angular_y = self.current_cmd_vel["angular_y"]
                angular_z = self.current_cmd_vel["angular_z"]
                
                # 发布速度命令
                self.publish_cmd_vel(
                    linear_x=linear_x,
                    linear_y=linear_y,
                    linear_z=linear_z,
                    angular_x=angular_x,
                    angular_y=angular_y,
                    angular_z=angular_z
                )
                
                # 等待下一次发布
                time.sleep(self.publish_interval)
                
            except Exception as e:
                logger.error(f"轮询发布时发生错误: {str(e)}")
                time.sleep(1)  # 出错时暂停一下
    
    def publish_cmd_vel(self, linear_x=0.0, linear_y=0.0, linear_z=0.0,
                       angular_x=0.0, angular_y=0.0, angular_z=0.0):
        """发布速度命令到cmd_vel话题
        
        Args:
            linear_x: X轴线速度 (m/s)
            linear_y: Y轴线速度 (m/s)
            linear_z: Z轴线速度 (m/s)
            angular_x: X轴角速度 (rad/s)
            angular_y: Y轴角速度 (rad/s)
            angular_z: Z轴角速度 (rad/s)
            
        Returns:
            成功时返回(True, 命令详情)，失败时返回(False, None)
        """
        if not self.connected:
            logger.warning("未连接到ROS，无法发布命令")
            return False, {"error": "未连接到ROS服务器"}
        
        if not self.ws:
            logger.error("WebSocket连接不存在")
            return False, {"error": "WebSocket连接不存在"}
        
        try:
            # 构建Twist消息
            twist_msg = {
                "linear": {
                    "x": linear_x,
                    "y": linear_y,
                    "z": linear_z
                },
                "angular": {
                    "x": angular_x,
                    "y": angular_y,
                    "z": angular_z
                }
            }
            
            # 构建发布消息
            publish_msg = {
                "op": "publish",
                "topic": "/cmd_vel",
                "msg": twist_msg
            }
            
            # 发送消息
            logger.debug(f"正在发送命令: {json.dumps(publish_msg)}")
            try:
                self.ws.send(json.dumps(publish_msg))
                logger.info(f"已发布cmd_vel消息: linear_x={linear_x}, angular_z={angular_z}")
            except websocket.WebSocketConnectionClosedException:
                logger.error("WebSocket连接已关闭，无法发送命令")
                self.connected = False
                return False, {"error": "WebSocket连接已关闭"}
            except Exception as send_error:
                logger.error(f"发送WebSocket消息时出错: {str(send_error)}")
                return False, {"error": f"发送WebSocket消息时出错: {str(send_error)}"}
            
            # 更新当前速度命令
            cmd_details = {
                "linear_x": linear_x,
                "linear_y": linear_y,
                "linear_z": linear_z,
                "angular_x": angular_x,
                "angular_y": angular_y,
                "angular_z": angular_z
            }
            self.current_cmd_vel = cmd_details
            
            # 如果速度不为零，启动轮询发布
            if (linear_x != 0 or linear_y != 0 or linear_z != 0 or 
                angular_x != 0 or angular_y != 0 or angular_z != 0):
                self.start_publish_loop()
            else:
                self.stop_publish_loop()
            
            return True, cmd_details
            
        except Exception as e:
            error_msg = f"发布命令时发生错误: {str(e)}"
            logger.error(error_msg)
            return False, {"error": error_msg}
    
    def process_decision(self, decision):
        """处理决策结果
        
        Args:
            decision: 决策结果字典，可能包含单个动作或动作序列
            
        Returns:
            成功时返回(True, 命令详情列表)，失败时返回(False, None)
        """
        # 检查是否为顺序动作
        if decision.get("is_sequence", False) and "actions" in decision and isinstance(decision["actions"], list):
            logger.info("ROSBridge: 检测到顺序动作，将依次执行每个动作")
        
        # 传递给movement_controller处理，它会处理单个动作和动作序列
        return self.movement_controller.process_decision(decision)
    
    def move_with_distance(self, linear_x, distance):
        """按指定距离移动"""
        return self.movement_controller.move_with_distance(linear_x, distance)
    
    def rotate_with_angle(self, angular_z, angle):
        """按指定角度旋转"""
        return self.movement_controller.rotate_with_angle(angular_z, angle)