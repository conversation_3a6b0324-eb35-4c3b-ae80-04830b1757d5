import sys
import json
import threading
import io
import re
import time 
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QTextEdit, QGroupBox, QGridLayout, QSlider, QSpinBox,
                             QComboBox, QDoubleSpinBox, QFrame, QSplitter, QLineEdit)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot
from PyQt5.QtGui import QFont, QTextCursor, QColor, QFontMetrics

def print_colored(text, color='green'):
    """打印彩色文本"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, colors['green'])}{text}{colors['end']}")

from audio_recognition import AudioRecognizer
from qwen_decision import QwenDecisionMaker

# 模拟的移动控制器类
class MockMovementController:
    """模拟的移动控制器，用于测试而不实际发送ROS命令"""
    
    def __init__(self):
        self.linear_speed = 0.5  # 默认线速度 m/s
        self.angular_speed = 0.5  # 默认角速度 rad/s
        print_colored("初始化模拟移动控制器...", "blue")
    
    def move_forward(self, distance=None, speed=None):
        """模拟前进"""
        if speed is None:
            speed = self.linear_speed
        
        if distance is None:
            print_colored(f"模拟: 以 {speed} m/s 的速度持续前进", "green")
            return True, {"command": "前进", "details": {"linear_x": speed, "angular_z": 0}}
        else:
            time_needed = distance / speed if speed > 0 else 0
            print_colored(f"模拟: 以 {speed} m/s 的速度前进 {distance} 米，预计需要 {time_needed:.2f} 秒", "green")
            return True, {"command": f"前进{distance}米", "details": {"linear_x": speed, "angular_z": 0}}
    
    def move_backward(self, distance=None, speed=None):
        """模拟后退"""
        if speed is None:
            speed = self.linear_speed
        
        if distance is None:
            print_colored(f"模拟: 以 {speed} m/s 的速度持续后退", "green")
            return True, {"command": "后退", "details": {"linear_x": -speed, "angular_z": 0}}
        else:
            time_needed = distance / speed if speed > 0 else 0
            print_colored(f"模拟: 以 {speed} m/s 的速度后退 {distance} 米，预计需要 {time_needed:.2f} 秒", "green")
            return True, {"command": f"后退{distance}米", "details": {"linear_x": -speed, "angular_z": 0}}
    
    def turn_left(self, angle=None, speed=None):
        """模拟左转"""
        if speed is None:
            speed = self.angular_speed
        
        if angle is None:
            print_colored(f"模拟: 以 {speed} rad/s 的角速度持续左转", "green")
            return True, {"command": "左转", "details": {"linear_x": 0, "angular_z": speed}}
        else:
            time_needed = angle * 3.14159 / (180 * speed) if speed > 0 else 0
            print_colored(f"模拟: 以 {speed} rad/s 的角速度左转 {angle} 度，预计需要 {time_needed:.2f} 秒", "green")
            return True, {"command": f"左转{angle}度", "details": {"linear_x": 0, "angular_z": speed}}
    
    def turn_right(self, angle=None, speed=None):
        """模拟右转"""
        if speed is None:
            speed = self.angular_speed
        
        if angle is None:
            print_colored(f"模拟: 以 {speed} rad/s 的角速度持续右转", "green")
            return True, {"command": "右转", "details": {"linear_x": 0, "angular_z": -speed}}
        else:
            time_needed = angle * 3.14159 / (180 * speed) if speed > 0 else 0
            print_colored(f"模拟: 以 {speed} rad/s 的角速度右转 {angle} 度，预计需要 {time_needed:.2f} 秒", "green")
            return True, {"command": f"右转{angle}度", "details": {"linear_x": 0, "angular_z": -speed}}
    
    def stop(self):
        """模拟停止"""
        print_colored("模拟: 停止移动", "yellow")
        return True, {"command": "停止", "details": {"linear_x": 0, "angular_z": 0}}
    
    def set_linear_speed(self, speed):
        """设置线速度"""
        self.linear_speed = speed
        print_colored(f"模拟: 设置线速度为 {speed} m/s", "blue")
        return True
    
    def set_angular_speed(self, speed):
        """设置角速度"""
        self.angular_speed = speed
        print_colored(f"模拟: 设置角速度为 {speed} rad/s", "blue")
        return True

# 模拟的ROS桥接发布器
class MockROSBridgePublisher:
    """模拟的ROS桥接发布器，用于测试而不实际连接ROS"""
    
    def __init__(self, ros_bridge_url="模拟URL"):
        self.ros_bridge_url = ros_bridge_url
        self.movement_controller = MockMovementController()
        self.connected = False
        print_colored(f"初始化模拟ROS桥接模块 (URL: {ros_bridge_url})...", "blue")
    
    def connect(self):
        """模拟连接到ROS服务器"""
        # 模拟连接延迟
        print_colored("模拟: 正在连接到ROS服务器...", "blue")
        time.sleep(1)  # 模拟连接延迟
        self.connected = True
        print_colored("模拟: 已连接到ROS服务器", "green")
        return True
    
    def disconnect(self):
        """模拟断开ROS连接"""
        if self.connected:
            print_colored("模拟: 断开ROS连接", "yellow")
            self.connected = False
            return True
        return False
    
    def publish_cmd_vel(self, linear_x=0.0, linear_y=0.0, linear_z=0.0, 
                       angular_x=0.0, angular_y=0.0, angular_z=0.0):
        """模拟发布速度命令"""
        if not self.connected:
            print_colored("模拟: 未连接到ROS服务器，无法发送命令", "red")
            return False
        
        print_colored(f"模拟: 发布速度命令 - 线速度({linear_x}, {linear_y}, {linear_z}) m/s, 角速度({angular_x}, {angular_y}, {angular_z}) rad/s", "green")
        return True
    
    def process_decision(self, decision_result):
        """处理决策结果并执行相应的机器人命令"""
        if not self.connected:
            print_colored("模拟: 未连接到ROS服务器，无法执行命令", "red")
            return False, []
        
        cmd_details_list = []
        
        # 处理新格式的决策结果（直接包含command字段）
        if "command" in decision_result:
            command = decision_result.get("command")
            if command:
                print_colored(f"模拟: 执行命令 '{command}'", "green")
                cmd_info = self._execute_command(command)
                if cmd_info:
                    cmd_details_list.append(cmd_info)
        
        # 处理旧格式的决策结果（包含status和commands字段）
        elif decision_result.get("status") == "success" and "commands" in decision_result:
            commands = decision_result.get("commands", [])
            for cmd in commands:
                print_colored(f"模拟: 执行命令 '{cmd}'", "green")
                cmd_info = self._execute_command(cmd)
                if cmd_info:
                    cmd_details_list.append(cmd_info)
        
        return True, cmd_details_list
    
    def _execute_command(self, command):
        """执行单个命令并返回命令详情"""
        # 前进命令
        if command == "前进":
            success, cmd_info = self.movement_controller.move_forward()
            return cmd_info if success else None
        
        # 后退命令
        elif command == "后退":
            success, cmd_info = self.movement_controller.move_backward()
            return cmd_info if success else None
        
        # 左转命令
        elif command == "左转":
            success, cmd_info = self.movement_controller.turn_left()
            return cmd_info if success else None
        
        # 右转命令
        elif command == "右转":
            success, cmd_info = self.movement_controller.turn_right()
            return cmd_info if success else None
        
        # 停止命令
        elif command == "停止":
            success, cmd_info = self.movement_controller.stop()
            return cmd_info if success else None
        
        # 带距离的前进命令
        elif re.match(r"前进(\d+(\.\d+)?)米", command):
            match = re.match(r"前进(\d+(\.\d+)?)米", command)
            distance = float(match.group(1))
            success, cmd_info = self.movement_controller.move_forward(distance=distance)
            return cmd_info if success else None
        
        # 带距离的后退命令
        elif re.match(r"后退(\d+(\.\d+)?)米", command):
            match = re.match(r"后退(\d+(\.\d+)?)米", command)
            distance = float(match.group(1))
            success, cmd_info = self.movement_controller.move_backward(distance=distance)
            return cmd_info if success else None
        
        # 带角度的左转命令
        elif re.match(r"左转(\d+(\.\d+)?)度", command):
            match = re.match(r"左转(\d+(\.\d+)?)度", command)
            angle = float(match.group(1))
            success, cmd_info = self.movement_controller.turn_left(angle=angle)
            return cmd_info if success else None
        
        # 带角度的右转命令
        elif re.match(r"右转(\d+(\.\d+)?)度", command):
            match = re.match(r"右转(\d+(\.\d+)?)度", command)
            angle = float(match.group(1))
            success, cmd_info = self.movement_controller.turn_right(angle=angle)
            return cmd_info if success else None
        
        # 未知命令
        else:
            print_colored(f"模拟: 未知命令 '{command}'", "red")
            return None

class TestRobotControlUI(QMainWindow):
    """测试用机器人控制Qt界面，不需要实际连接ROS"""
    
    # 自定义信号
    update_status_signal = pyqtSignal(str, str)  # 参数：消息内容，颜色
    update_recognition_signal = pyqtSignal(str)
    update_decision_signal = pyqtSignal(str)
    update_thinking_signal = pyqtSignal(str)  # 用于更新思考过程
    update_ros_signal = pyqtSignal(str, str)  # 参数：消息内容，颜色
    
    def __init__(self):
        super().__init__()
        
        # 初始化模块
        print_colored("初始化语音识别模块...", "blue")
        self.recognizer = AudioRecognizer()
        
        print_colored("初始化决策分析模块...", "blue")
        self.decision_maker = QwenDecisionMaker()
        
        print_colored("初始化模拟ROS桥接模块...", "blue")
        self.ros_publisher = MockROSBridgePublisher()
        self.movement_controller = self.ros_publisher.movement_controller
        
        # 连接状态
        self.ros_connected = False
        
        # 录音时长
        self.record_duration = 3
        
        # 速度和距离设置
        self.current_speed = 0.5  # 默认速度0.5m/s
        self.current_distance = 1.0  # 默认距离1米
        
        # 初始化UI
        self.init_ui()
        
        # 连接信号到槽
        self.update_status_signal.connect(self.update_status)
        self.update_recognition_signal.connect(self.update_recognition_text)
        self.update_decision_signal.connect(self.update_decision_text)
        self.update_thinking_signal.connect(self.update_thinking_text)
        self.update_ros_signal.connect(self.update_ros_status)
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle('测试用机器人控制系统 (无ROS连接)')
        self.setGeometry(100, 100, 1000, 700)
    
        # 应用样式表
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0; /* 浅灰色背景 */
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #cccccc; /* 灰色边框 */
                border-radius: 5px;
                margin-top: 10px;
                background-color: #ffffff; /* 白色背景 */
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 3px;
                background-color: #e0e0e0; /* 标题背景 */
                border-radius: 3px;
            }
            QPushButton {
                background-color: #4CAF50; /* 绿色 */
                color: white;
                border: none;
                padding: 8px 16px;
                text-align: center;
                text-decoration: none;
                font-size: 14px;
                margin: 4px 2px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049; /* 深绿色悬停 */
            }
            QPushButton:pressed {
                background-color: #3e8e41; /* 更深绿色按下 */
            }
            QPushButton#record_button {
                background-color: #f44336; /* 红色录音按钮 */
            }
            QPushButton#record_button:hover {
                background-color: #da190b;
            }
            QPushButton#record_button:pressed {
                background-color: #c21807;
            }
            QPushButton#connect_button {
                background-color: #2196F3; /* 蓝色连接按钮 */
            }
            QPushButton#connect_button:hover {
                background-color: #0b7dda;
            }
            QPushButton#connect_button:pressed {
                background-color: #0a6ebd;
            }
            QTextEdit {
                border: 1px solid #cccccc;
                border-radius: 3px;
                background-color: #f8f8f8;
            }
            QLabel {
                font-size: 13px;
            }
            QSlider::groove:horizontal {
                border: 1px solid #bbb;
                background: white;
                height: 8px;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #4CAF50;
                border: 1px solid #4CAF50;
                width: 18px;
                margin: -2px 0; 
                border-radius: 9px;
            }
            QSpinBox, QDoubleSpinBox, QComboBox, QLineEdit {
                padding: 3px;
                border: 1px solid #cccccc;
                border-radius: 3px;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png); /* 可选：自定义下拉箭头 */
            }
        """)
    
        # 创建中央部件和主布局
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        
        # 状态栏
        self.status_label = QLabel('准备就绪 - 测试模式')
        self.status_label.setStyleSheet('color: green; font-weight: bold;')
        main_layout.addWidget(self.status_label)
        
        # 创建上部布局（语音识别和决策分析）
        top_layout = QHBoxLayout()
        
        # 语音识别部分
        recognition_group = QGroupBox('语音识别')
        recognition_layout = QVBoxLayout(recognition_group)
        
        # 录音按钮和时长设置
        record_layout = QHBoxLayout()
        self.record_button = QPushButton('开始录音')
        self.record_button.setObjectName('record_button') # 添加对象名称以便样式表识别
        self.record_button.clicked.connect(self.start_recognition)
        record_layout.addWidget(self.record_button)
        
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel('录音时长:'))
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 10)
        self.duration_spin.setValue(self.record_duration)
        self.duration_spin.valueChanged.connect(self.update_duration)
        duration_layout.addWidget(self.duration_spin)
        duration_layout.addWidget(QLabel('秒'))
        record_layout.addLayout(duration_layout)
        
        recognition_layout.addLayout(record_layout)
        
        # 识别结果显示
        recognition_layout.addWidget(QLabel('识别结果:'))
        self.recognition_text = QTextEdit()
        self.recognition_text.setReadOnly(True)
        recognition_layout.addWidget(self.recognition_text)
        
        top_layout.addWidget(recognition_group)
        
        # 决策分析部分
        decision_group = QGroupBox('决策分析')
        decision_layout = QVBoxLayout(decision_group)
        
        # 添加思考过程显示区域
        decision_layout.addWidget(QLabel('思考过程:'))
        self.thinking_text = QTextEdit()
        self.thinking_text.setReadOnly(True)
        decision_layout.addWidget(self.thinking_text)
        
        # 添加分隔线
        line_thinking = QFrame()
        line_thinking.setFrameShape(QFrame.HLine)
        line_thinking.setFrameShadow(QFrame.Sunken)
        decision_layout.addWidget(line_thinking)
        
        # 分析结果显示区域
        decision_layout.addWidget(QLabel('分析结果:'))
        self.decision_text = QTextEdit()
        self.decision_text.setReadOnly(True)
        decision_layout.addWidget(self.decision_text)
        
        top_layout.addWidget(decision_group)
        
        main_layout.addLayout(top_layout)
        
        # 创建中部布局（速度控制面板）
        middle_layout = QHBoxLayout()
        
        # 速度控制面板
        speed_group = QGroupBox('速度控制')
        speed_layout = QVBoxLayout(speed_group)
        
        # 线速度控制
        linear_speed_layout = QHBoxLayout()
        linear_speed_layout.addWidget(QLabel('线速度:'))
        self.linear_speed_slider = QSlider(Qt.Horizontal)
        self.linear_speed_slider.setRange(0, 100)  # 0-1.0 m/s，乘以100
        self.linear_speed_slider.setValue(int(self.current_speed * 100))
        self.linear_speed_slider.valueChanged.connect(self.update_linear_speed)
        linear_speed_layout.addWidget(self.linear_speed_slider)
        
        self.linear_speed_label = QLabel(f'{self.current_speed:.2f} m/s')
        linear_speed_layout.addWidget(self.linear_speed_label)
        speed_layout.addLayout(linear_speed_layout)
        
        # 角速度控制
        angular_speed_layout = QHBoxLayout()
        angular_speed_layout.addWidget(QLabel('角速度:'))
        self.angular_speed_slider = QSlider(Qt.Horizontal)
        self.angular_speed_slider.setRange(0, 100)  # 0-1.0 rad/s，乘以100
        self.angular_speed_slider.setValue(50)  # 默认0.5 rad/s
        self.angular_speed_slider.valueChanged.connect(self.update_angular_speed)
        angular_speed_layout.addWidget(self.angular_speed_slider)
        
        self.angular_speed_label = QLabel('0.50 rad/s')
        angular_speed_layout.addWidget(self.angular_speed_label)
        speed_layout.addLayout(angular_speed_layout)
        
        # 添加分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        speed_layout.addWidget(line)
        
        # 预设速度选择
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel('预设速度:'))
        self.preset_combo = QComboBox()
        self.preset_combo.addItems(['慢速 (0.2 m/s)', '中速 (0.5 m/s)', '快速 (0.8 m/s)'])
        self.preset_combo.setCurrentIndex(1)  # 默认中速
        self.preset_combo.currentIndexChanged.connect(self.select_preset_speed)
        preset_layout.addWidget(self.preset_combo)
        speed_layout.addLayout(preset_layout)
        
        middle_layout.addWidget(speed_group)
        
        # 距离和角度控制面板
        distance_group = QGroupBox('距离和角度控制')
        distance_layout = QVBoxLayout(distance_group)
        
        # 距离控制
        distance_input_layout = QHBoxLayout()
        distance_input_layout.addWidget(QLabel('移动距离:'))
        self.distance_spin = QDoubleSpinBox()
        self.distance_spin.setRange(0.1, 10.0)
        self.distance_spin.setSingleStep(0.1)
        self.distance_spin.setValue(self.current_distance)
        self.distance_spin.valueChanged.connect(self.update_distance)
        distance_input_layout.addWidget(self.distance_spin)
        distance_input_layout.addWidget(QLabel('米'))
        distance_layout.addLayout(distance_input_layout)
        
        # 添加分隔线
        line2 = QFrame()
        line2.setFrameShape(QFrame.HLine)
        line2.setFrameShadow(QFrame.Sunken)
        distance_layout.addWidget(line2)
        
        # 精确移动按钮
        precise_layout = QHBoxLayout()
        self.move_distance_btn = QPushButton('前进指定距离')
        self.move_distance_btn.clicked.connect(self.move_with_distance)
        precise_layout.addWidget(self.move_distance_btn)
        distance_layout.addLayout(precise_layout)
        
        middle_layout.addWidget(distance_group)
        
        main_layout.addLayout(middle_layout)
        
        # 创建下部布局（模拟连接和手动控制）
        bottom_layout = QHBoxLayout()
        
        # 模拟连接状态
        ros_group = QGroupBox('模拟ROS连接')
        ros_layout = QVBoxLayout(ros_group)
        
        # 连接按钮和状态
        ros_conn_layout = QHBoxLayout()
        self.connect_button = QPushButton('模拟连接ROS')
        self.connect_button.setObjectName('connect_button') # 添加对象名称以便样式表识别
        self.connect_button.clicked.connect(self.connect_to_ros)
        ros_conn_layout.addWidget(self.connect_button)
        
        self.ros_status = QLabel('未连接')
        self.ros_status.setStyleSheet('color: red; font-weight: bold;')
        ros_conn_layout.addWidget(self.ros_status)
        
        ros_layout.addLayout(ros_conn_layout)
        
        # 添加模拟URL设置
        ros_url_layout = QHBoxLayout()
        ros_url_layout.addWidget(QLabel('模拟URL:'))
        self.ros_url_input = QLineEdit(self.ros_publisher.ros_bridge_url)
        ros_url_layout.addWidget(self.ros_url_input)
        self.apply_url_btn = QPushButton('应用')
        self.apply_url_btn.clicked.connect(self.update_ros_bridge_url)
        ros_url_layout.addWidget(self.apply_url_btn)
        ros_layout.addLayout(ros_url_layout)
        
        # 模拟命令日志
        ros_layout.addWidget(QLabel('模拟命令日志:'))
        self.ros_log = QTextEdit()
        self.ros_log.setReadOnly(True)
        ros_layout.addWidget(self.ros_log)
        
        bottom_layout.addWidget(ros_group)
        
        # 手动控制面板
        control_group = QGroupBox('手动控制')
        control_layout = QGridLayout(control_group)
        
        # 方向按钮
        self.forward_btn = QPushButton('前进')
        self.forward_btn.pressed.connect(lambda: self.manual_control('前进'))
        self.forward_btn.released.connect(lambda: self.manual_control('停止'))
        
        self.backward_btn = QPushButton('后退')
        self.backward_btn.pressed.connect(lambda: self.manual_control('后退'))
        self.backward_btn.released.connect(lambda: self.manual_control('停止'))
        
        self.left_btn = QPushButton('左转')
        self.left_btn.pressed.connect(lambda: self.manual_control('左转'))
        self.left_btn.released.connect(lambda: self.manual_control('停止'))
        
        self.right_btn = QPushButton('右转')
        self.right_btn.pressed.connect(lambda: self.manual_control('右转'))
        self.right_btn.released.connect(lambda: self.manual_control('停止'))
        
        self.stop_btn = QPushButton('停止')
        self.stop_btn.clicked.connect(lambda: self.manual_control('停止'))
        
        # 放置按钮
        control_layout.addWidget(self.forward_btn, 0, 1)
        control_layout.addWidget(self.left_btn, 1, 0)
        control_layout.addWidget(self.stop_btn, 1, 1)
        control_layout.addWidget(self.right_btn, 1, 2)
        control_layout.addWidget(self.backward_btn, 2, 1)
        
        # 添加自定义命令区域
        custom_layout = QVBoxLayout()
        custom_layout.addWidget(QLabel('自定义命令:'))
        
        # 命令选择下拉框
        command_layout = QHBoxLayout()
        command_layout.addWidget(QLabel('命令:'))
        self.command_combo = QComboBox()
        self.command_combo.addItems(['前进', '后退', '左转', '右转', '停止'])
        command_layout.addWidget(self.command_combo)
        custom_layout.addLayout(command_layout)
        
        # 执行按钮
        self.execute_btn = QPushButton('执行命令')
        self.execute_btn.clicked.connect(self.execute_custom_command)
        custom_layout.addWidget(self.execute_btn)
        
        control_layout.addLayout(custom_layout, 3, 0, 1, 3)
        
        bottom_layout.addWidget(control_group)
        
        main_layout.addLayout(bottom_layout)
        
        # 设置中央部件
        self.setCentralWidget(central_widget)
        
        # 初始禁用控制按钮
        self.set_control_enabled(False)
    
    def set_control_enabled(self, enabled):
        """启用或禁用控制按钮"""
        self.forward_btn.setEnabled(enabled)
        self.backward_btn.setEnabled(enabled)
        self.left_btn.setEnabled(enabled)
        self.right_btn.setEnabled(enabled)
        self.stop_btn.setEnabled(enabled)
        self.move_distance_btn.setEnabled(enabled)
        self.execute_btn.setEnabled(enabled)
        self.linear_speed_slider.setEnabled(enabled)
        self.angular_speed_slider.setEnabled(enabled)
        self.preset_combo.setEnabled(enabled)
    
    def update_duration(self, value):
        """更新录音时长"""
        self.record_duration = value
    
    def update_linear_speed(self, value):
        """更新线速度"""
        self.current_speed = value / 100.0
        self.linear_speed_label.setText(f'{self.current_speed:.2f} m/s')
        self.movement_controller.set_linear_speed(self.current_speed)
    
    def update_angular_speed(self, value):
        """更新角速度"""
        angular_speed = value / 100.0
        self.angular_speed_label.setText(f'{angular_speed:.2f} rad/s')
        self.movement_controller.set_angular_speed(angular_speed)
    
    def update_distance(self, value):
        """更新移动距离"""
        self.current_distance = value
    
    def select_preset_speed(self, index):
        """选择预设速度"""
        if index == 0:  # 慢速
            self.current_speed = 0.2
        elif index == 1:  # 中速
            self.current_speed = 0.5
        elif index == 2:  # 快速
            self.current_speed = 0.8
        
        # 更新滑块和标签
        self.linear_speed_slider.setValue(int(self.current_speed * 100))
        self.linear_speed_label.setText(f'{self.current_speed:.2f} m/s')
        # 更新移动控制器速度
        self.movement_controller.set_linear_speed(self.current_speed)
    
    def connect_to_ros(self):
        """模拟连接到ROS服务器"""
        self.update_status_signal.emit('正在模拟连接到ROS服务器...', 'blue')
        
        # 在新线程中连接，避免阻塞UI
        print_colored("正在模拟连接到ROS服务器...", "blue")
        threading.Thread(target=self._connect_ros_thread).start()
    
    def _connect_ros_thread(self):
        """模拟ROS连接线程"""
        try:
            time.sleep(1)  # 模拟连接延迟
            
            if self.ros_publisher.connect():
                self.ros_connected = True
                print_colored("模拟: 已连接到ROS服务器", "green")
                self.update_ros_signal.emit('已连接', 'green')
                self.update_status_signal.emit('已模拟连接到ROS服务器', 'green')
                self.set_control_enabled(True)
            else:
                self.ros_connected = False
                print_colored("模拟: 无法连接到ROS服务器", "red")
                self.update_ros_signal.emit('连接失败', 'red')
                self.update_status_signal.emit('模拟ROS连接失败', 'red')
        except Exception as e:
            self.ros_connected = False
            print_colored(f"模拟: 连接ROS时出错: {str(e)}", "red")
            self.update_ros_signal.emit('连接错误', 'red')
            self.update_status_signal.emit(f'模拟ROS连接错误: {str(e)}', 'red')
    
    def update_ros_bridge_url(self):
        """更新ROS桥接URL"""
        new_url = self.ros_url_input.text()
        if new_url:
            self.ros_publisher.ros_bridge_url = new_url
            print_colored(f"模拟: 已更新ROS桥接URL为: {new_url}", "blue")
            self.update_ros_signal.emit(f'已更新URL: {new_url}', 'blue')
    
    def start_recognition(self):
        """开始语音识别"""
        if self.record_button.text() == '开始录音':
            self.record_button.setText('停止录音')
            self.record_button.setStyleSheet('background-color: red;')
            self.update_status_signal.emit('正在录音...', 'red')
            
            # 在新线程中录音，避免阻塞UI
            threading.Thread(target=self._record_thread).start()
        else:
            # 这里可以添加停止录音的逻辑，但目前简单实现，不支持提前停止
            pass
    
    def _record_thread(self):
        """录音线程"""
        try:
            # 录制音频
            print_colored(f"正在录音，持续 {self.record_duration} 秒...", "yellow")
            audio_file = self.recognizer.record_audio(duration=self.record_duration)
            
            # 语音识别
            print_colored("正在进行语音识别...", "blue")
            self.update_status_signal.emit('正在识别...', 'blue')
            text_result = self.recognizer.recognize_speech(audio_file)
            
            if not text_result:
                print_colored("未能识别到语音内容", "red")
                self.update_status_signal.emit('未能识别到语音内容', 'red')
                self.update_recognition_signal.emit("未能识别到语音内容")
            else:
                print_colored(f"识别结果: {text_result}", "green")
                self.update_status_signal.emit('识别完成', 'green')
                self.update_recognition_signal.emit(text_result)
                
                # 决策分析
                print_colored("正在进行决策分析...", "blue")
                self.update_status_signal.emit('正在分析...', 'blue')
                
                # 获取思考过程
                thinking_process = self.decision_maker.think_aloud(text_result, play_thinking=False)
                if thinking_process:
                    self.update_thinking_signal.emit(thinking_process)
                
                # 获取决策结果
                decision_result = self.decision_maker.make_decision(text_result)
                
                # 输出决策结果
                print_colored("\n决策分析结果:", "purple")
                decision_json = json.dumps(decision_result, ensure_ascii=False, indent=2)
                print(decision_json)
                self.update_decision_signal.emit(decision_json)
                
                # 如果已连接ROS，则执行命令
                if self.ros_connected:
                    print_colored("\n正在执行模拟ROS命令...", "blue")
                    self.update_status_signal.emit('正在执行模拟命令...', 'blue')
                    
                    success, cmd_details_list = self.ros_publisher.process_decision(decision_result)
                    if success:
                        print_colored("模拟: ROS命令执行成功", "green")
                        self.update_status_signal.emit('模拟命令执行成功', 'green')
                        
                        # 显示执行的命令详情
                        if cmd_details_list:
                            for cmd_info in cmd_details_list:
                                cmd_name = cmd_info.get("command")
                                details = cmd_info.get("details")
                                log_msg = f"命令: {cmd_name}\n"
                                
                                if details:
                                    # 显示线速度信息
                                    if "linear_x" in details and details["linear_x"] != 0:
                                        log_msg += f"  X轴线速度: {details['linear_x']} m/s\n"
                                    # 显示角速度信息
                                    if "angular_z" in details and details["angular_z"] != 0:
                                        log_msg += f"  Z轴角速度: {details['angular_z']} rad/s\n"
                                
                                self.update_ros_signal.emit(log_msg, 'green')
                    else:
                        print_colored("模拟: ROS命令执行失败", "red")
                        self.update_status_signal.emit('模拟命令执行失败', 'red')
                        self.update_ros_signal.emit("命令执行失败", 'red')
                else:
                    print_colored("未连接到模拟ROS服务器，无法执行命令", "yellow")
                    self.update_status_signal.emit('未连接到模拟ROS服务器', 'yellow')
        except Exception as e:
            print_colored(f"录音或识别过程出错: {str(e)}", "red")
            self.update_status_signal.emit(f'错误: {str(e)}', 'red')
        finally:
            # 恢复按钮状态
            self.record_button.setText('开始录音')
            self.record_button.setStyleSheet('')
    
    def update_status(self, message, color='green'):
        """更新状态栏"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet(f'color: {color}; font-weight: bold;')
    
    def update_recognition_text(self, text):
        """更新识别结果文本"""
        self.recognition_text.setText(text)
        # 滚动到底部
        self.recognition_text.moveCursor(QTextCursor.End)
    
    def update_decision_text(self, text):
        """更新决策结果文本"""
        self.decision_text.setText(text)
        # 滚动到底部
        self.decision_text.moveCursor(QTextCursor.End)
    
    def update_thinking_text(self, text):
        """更新思考过程文本"""
        self.thinking_text.setText(text)
        # 滚动到底部
        self.thinking_text.moveCursor(QTextCursor.End)
    
    def update_ros_status(self, message, color='green'):
        """更新ROS状态和日志"""
        if message == '已连接':
            self.ros_status.setText('已连接')
            self.ros_status.setStyleSheet('color: green; font-weight: bold;')
            self.connect_button.setText('断开连接')
        elif message == '未连接' or message == '连接失败' or message == '连接错误':
            self.ros_status.setText(message)
            self.ros_status.setStyleSheet('color: red; font-weight: bold;')
            self.connect_button.setText('模拟连接ROS')
        else:
            # 添加到日志
            cursor = self.ros_log.textCursor()
            cursor.movePosition(QTextCursor.End)
            cursor.insertText(message + '\n')
            self.ros_log.setTextCursor(cursor)
            # 滚动到底部
            self.ros_log.ensureCursorVisible()
    
    def manual_control(self, command):
        """手动控制机器人"""
        if not self.ros_connected:
            print_colored("未连接到模拟ROS服务器，无法执行命令", "red")
            self.update_status_signal.emit('未连接到模拟ROS服务器', 'red')
            return
        
        print_colored(f"模拟: 执行手动控制命令 '{command}'", "blue")
        
        # 执行命令
        cmd_info = self.ros_publisher._execute_command(command)
        if cmd_info:
            # 更新日志
            log_msg = f"手动命令: {cmd_info['command']}\n"
            details = cmd_info.get("details")
            if details:
                # 显示线速度信息
                if "linear_x" in details and details["linear_x"] != 0:
                    log_msg += f"  X轴线速度: {details['linear_x']} m/s\n"
                # 显示角速度信息
                if "angular_z" in details and details["angular_z"] != 0:
                    log_msg += f"  Z轴角速度: {details['angular_z']} rad/s\n"
            
            self.update_ros_signal.emit(log_msg, 'blue')
    
    def move_with_distance(self):
        """按指定距离移动"""
        if not self.ros_connected:
            print_colored("未连接到模拟ROS服务器，无法执行命令", "red")
            self.update_status_signal.emit('未连接到模拟ROS服务器', 'red')
            return
        
        distance = self.current_distance
        command = f"前进{distance}米"
        print_colored(f"模拟: 执行精确移动命令 '{command}'", "blue")
        
        # 执行命令
        success, cmd_info = self.movement_controller.move_forward(distance=distance)
        if success and cmd_info:
            # 更新日志
            log_msg = f"精确移动: {cmd_info['command']}\n"
            details = cmd_info.get("details")
            if details:
                # 显示线速度信息
                if "linear_x" in details and details["linear_x"] != 0:
                    log_msg += f"  X轴线速度: {details['linear_x']} m/s\n"
                # 计算预计时间
                speed = details.get("linear_x", 0)
                if speed > 0:
                    time_needed = distance / speed
                    log_msg += f"  预计移动时间: {time_needed:.2f}秒\n"
            
            self.update_ros_signal.emit(log_msg, 'blue')
    
    def execute_custom_command(self):
        """执行自定义命令"""
        if not self.ros_connected:
            print_colored("未连接到模拟ROS服务器，无法执行命令", "red")
            self.update_status_signal.emit('未连接到模拟ROS服务器', 'red')
            return
        
        command = self.command_combo.currentText()
        print_colored(f"模拟: 执行自定义命令 '{command}'", "blue")
        
        # 执行命令
        cmd_info = self.ros_publisher._execute_command(command)
        if cmd_info:
            # 更新日志
            log_msg = f"自定义命令: {cmd_info['command']}\n"
            details = cmd_info.get("details")
            if details:
                # 显示线速度信息
                if "linear_x" in details and details["linear_x"] != 0:
                    log_msg += f"  X轴线速度: {details['linear_x']} m/s\n"
                # 显示角速度信息
                if "angular_z" in details and details["angular_z"] != 0:
                    log_msg += f"  Z轴角速度: {details['angular_z']} rad/s\n"
            
            self.update_ros_signal.emit(log_msg, 'blue')

def run_test_interface():
    """运行测试用界面"""
    app = QApplication(sys.argv)
    window = TestRobotControlUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    import time  # 确保time模块被导入
    run_test_interface()