import os
import time
import pyaudio
import wave
import json
from dotenv import load_dotenv
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.asr.v20190614 import asr_client, models

# 加载环境变量
load_dotenv()

class AudioRecognizer:
    def __init__(self):
        # 腾讯云API认证信息
        self.secret_id = os.getenv('SECRET_ID')
        self.secret_key = os.getenv('SECRET_KEY')
        self.app_id = int(os.getenv('APP_ID', 0))
        
        # 音频参数设置
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        self.chunk = 1024
        self.record_seconds = 10  # 默认录音时长
        
        # 初始化腾讯云客户端
        self._init_tencent_client()
    
    def _init_tencent_client(self):
        """初始化腾讯云ASR客户端"""
        try:
            cred = credential.Credential(self.secret_id, self.secret_key)
            http_profile = HttpProfile()
            http_profile.endpoint = "asr.tencentcloudapi.com"
            
            client_profile = ClientProfile()
            client_profile.httpProfile = http_profile
            
            self.client = asr_client.AsrClient(cred, "ap-chongqing", client_profile)
        except Exception as e:
            print(f"初始化腾讯云客户端失败: {e}")
            raise
    
    def record_audio(self, output_file="temp_recording.wav", duration=5):
        """从麦克风录制音频
        
        Args:
            output_file: 输出的音频文件路径
            duration: 录音时长(秒)，如果为None则使用默认时长
        
        Returns:
            录音文件路径
        """
        if duration is not None:
            self.record_seconds = duration
            
        print(f"开始录音，持续 {self.record_seconds} 秒...")
        
        audio = pyaudio.PyAudio()
        
        # 打开音频流
        stream = audio.open(
            format=self.format,
            channels=self.channels,
            rate=self.rate,
            input=True,
            frames_per_buffer=self.chunk
        )
        
        # 录制音频
        frames = []
        for i in range(0, int(self.rate / self.chunk * self.record_seconds)):
            data = stream.read(self.chunk)
            frames.append(data)
        
        print("录音结束")
        
        # 停止并关闭音频流
        stream.stop_stream()
        stream.close()
        audio.terminate()
        
        # 保存音频文件
        wf = wave.open(output_file, 'wb')
        wf.setnchannels(self.channels)
        wf.setsampwidth(audio.get_sample_size(self.format))
        wf.setframerate(self.rate)
        wf.writeframes(b''.join(frames))
        wf.close()
        
        return output_file
    
    def recognize_speech(self, audio_file):
        """使用腾讯云ASR识别音频文件中的语音
        
        Args:
            audio_file: 音频文件路径
            
        Returns:
            识别结果文本
        """
        try:
            # 读取音频文件并转为base64编码
            with open(audio_file, 'rb') as f:
                audio_data = f.read()
            
            # 创建请求对象
            req = models.SentenceRecognitionRequest()
            req.ProjectId = self.app_id
            req.SubServiceType = 2  # 一句话识别
            req.EngSerViceType = "16k_zh"  # 中文普通话通用
            req.SourceType = 1  # 音频数据来源（0：语音URL，1：语音数据）
            req.VoiceFormat = "wav"  # 音频格式
            req.UsrAudioKey = "temp_audio_" + str(int(time.time()))
            
            import base64
            req.Data = base64.b64encode(audio_data).decode('utf-8')
            
            # 发送请求
            resp = self.client.SentenceRecognition(req)
            result = json.loads(resp.to_json_string())
            
            # 提取识别结果
            if 'Result' in result:
                return result['Result']
            else:
                print(f"未找到识别结果: {result}")
                return ""
                
        except TencentCloudSDKException as err:
            print(f"腾讯云API调用失败: {err}")
            return ""
        except Exception as e:
            print(f"语音识别过程中发生错误: {e}")
            return ""

# 测试代码
if __name__ == "__main__":
    recognizer = AudioRecognizer()
    audio_file = recognizer.record_audio(duration=5)
    result = recognizer.recognize_speech(audio_file)
    print(f"识别结果: {result}")