import threading
import logging
import time
import re

logger = logging.getLogger(__name__)

class MovementController:
    """机器人运动控制器"""
   
    def __init__(self, ros_bridge):
        self.ros_bridge = ros_bridge
        self.timed_stop_thread = None
        self.timed_stop_event = threading.Event()
        self.sequence_thread = None
        self.sequence_stop_event = threading.Event()
        self.distance_scale = 0.2  # 每米对应的线速度
        self.speed_scale = 1.0  # PWM速度比例尺：linear_x=1对应实际速度1m/s
        self.max_speed = 1.0  # 最大速度限制
        # 命令速度映射表 - 用于UI界面中的按钮控制，被integrated_qt_interface.py使用
        self.command_velocity_map = {
            "前进": {
                "linear": {"x": 0.5, "y": 0.0, "z": 0.0},  # 直接对应0.5m/s
                "angular": {"x": 0.0, "y": 0.0, "z": 0.0}
            },
            "后退": {
                "linear": {"x": -0.5, "y": 0.0, "z": 0.0},  # 直接对应-0.5m/s
                "angular": {"x": 0.0, "y": 0.0, "z": 0.0}
            },
            "左转": {
                "linear": {"x": 0.2, "y": 0.0, "z": 0.0},  # 添加0.2m/s的前进速度
                "angular": {"x": 0.0, "y": 0.0, "z": 0.5}  # 保持与teleop代码一致的转向速度
            },
            "右转": {
                "linear": {"x": 0.2, "y": 0.0, "z": 0.0},  # 添加0.2m/s的前进速度
                "angular": {"x": 0.0, "y": 0.0, "z": -0.5}  # 保持与teleop代码一致的转向速度
            },
            "停止": {
                "linear": {"x": 0.0, "y": 0.0, "z": 0.0},
                "angular": {"x": 0.0, "y": 0.0, "z": 0.0}
            }
        }
   
    def cancel_timed_stop(self):
        """取消定时停止"""
        if self.timed_stop_thread and self.timed_stop_thread.is_alive():
            self.timed_stop_event.set()
            self.timed_stop_thread.join()
            self.timed_stop_event.clear()
    
    def cancel_sequence(self):
        """取消正在执行的动作序列"""
        if self.sequence_thread and self.sequence_thread.is_alive():
            self.sequence_stop_event.set()
            self.sequence_thread.join()
            self.sequence_stop_event.clear()
            # 停止机器人当前动作
            self.ros_bridge.publish_cmd_vel()
            logger.info("已取消动作序列执行")
   
    def _timed_stop_task(self, duration):
        """定时停止任务"""
        logger.info(f"定时停止任务开始，将在 {duration:.2f} 秒后停止")
        try:
            if not self.timed_stop_event.wait(duration):
                # 如果事件没有被设置（即没有提前取消），则停止机器人
                logger.info(f"定时停止：时间到，准备停止机器人")
                self.ros_bridge.publish_cmd_vel()  # 发送零速度命令
                logger.info("定时停止：已停止机器人")
        except Exception as e:
            logger.error(f"定时停止任务出错: {str(e)}")
            # 确保机器人停止
            try:
                self.ros_bridge.publish_cmd_vel()
            except:
                pass

    def _execute_sequence_task(self, actions):
        """顺序执行动作序列
        
        Args:
            actions: 动作列表，每个动作包含速度参数和持续时间
        """
        logger.info(f"开始执行动作序列，共 {len(actions)} 个动作")
        
        try:
            for i, action in enumerate(actions):
                # 检查是否需要停止序列执行
                if self.sequence_stop_event.is_set():
                    logger.info("动作序列执行被取消")
                    break
                
                # 提取当前动作参数
                description = action.get("description", f"动作 {i+1}")
                duration = float(action.get("duration", 0))
                linear_x = float(action.get("linear_x", 0.0))
                linear_y = float(action.get("linear_y", 0.0))
                linear_z = float(action.get("linear_z", 0.0))
                angular_x = float(action.get("angular_x", 0.0))
                angular_y = float(action.get("angular_y", 0.0))
                angular_z = float(action.get("angular_z", 0.0))
                
                logger.info(f"执行动作 {i+1}/{len(actions)}: {description}")
                logger.info(f"  参数: 持续时间={duration}秒, linear_x={linear_x}, angular_z={angular_z}")
                
                # 在执行新动作前，确保机器人已停止（避免动作叠加）
                if i > 0:
                    # 先发送停止命令
                    self.ros_bridge.publish_cmd_vel()
                    # 短暂等待机器人停止
                    time.sleep(0.5)
                
                # 发布速度命令
                success, cmd_details = self.ros_bridge.publish_cmd_vel(
                    linear_x=linear_x,
                    linear_y=linear_y,
                    linear_z=linear_z,
                    angular_x=angular_x,
                    angular_y=angular_y,
                    angular_z=angular_z
                )
                
                if not success:
                    logger.error(f"动作 {i+1} 执行失败: {cmd_details.get('error', '未知错误')}")
                    break
                
                # 等待指定时间
                if duration > 0:
                    logger.info(f"动作 {i+1} 将持续 {duration} 秒")
                    # 使用一个可中断的等待，而不是简单的 time.sleep
                    if self.sequence_stop_event.wait(duration):
                        logger.info("动作序列执行被取消")
                        break
                else:
                    # 如果没有指定持续时间，使用默认的短暂执行时间
                    time.sleep(0.5)
                
                # 每个动作执行完毕后，确认该动作已完成
                logger.info(f"动作 {i+1} 已完成")
                
                # 如果不是最后一个动作，在动作之间添加明确的停顿
                if i < len(actions) - 1:
                    # 发送停止命令，确保当前动作完全停止
                    self.ros_bridge.publish_cmd_vel()
                    # 动作之间的停顿，给机器人足够的时间停止
                    time.sleep(0.5)
                    logger.info(f"准备执行下一个动作 {i+2}/{len(actions)}")
            
            # 序列执行完毕后停止机器人
            logger.info("动作序列执行完毕，停止机器人")
            self.ros_bridge.publish_cmd_vel()
            
        except Exception as e:
            logger.error(f"动作序列执行出错: {str(e)}")
            # 确保机器人停止
            try:
                self.ros_bridge.publish_cmd_vel()
            except:
                pass
    
    def execute_sequence(self, actions):
        """执行动作序列
        
        Args:
            actions: 动作列表，每个动作包含速度参数和持续时间
            
        Returns:
            成功时返回(True, 命令详情列表)，失败时返回(False, None)
        """
        if not actions or not isinstance(actions, list):
            logger.error("动作序列为空或格式不正确")
            return False, None
        
        # 先取消正在执行的序列（如果有）
        self.cancel_sequence()
        
        # 创建并启动序列执行线程
        self.sequence_thread = threading.Thread(
            target=self._execute_sequence_task,
            args=(actions,)
        )
        self.sequence_thread.daemon = True
        self.sequence_stop_event.clear()
        self.sequence_thread.start()
        
        # 构建命令详情列表
        cmd_details = []
        for i, action in enumerate(actions):
            description = action.get("description", f"动作 {i+1}")
            duration = float(action.get("duration", 0))
            cmd_details.append({
                "command": description,
                "duration": f"持续{duration}秒",
                "details": {
                    "linear_x": action.get("linear_x", 0.0),
                    "linear_y": action.get("linear_y", 0.0),
                    "linear_z": action.get("linear_z", 0.0),
                    "angular_x": action.get("angular_x", 0.0),
                    "angular_y": action.get("angular_y", 0.0),
                    "angular_z": action.get("angular_z", 0.0)
                }
            })
        
        return True, cmd_details
    
    def process_decision(self, decision):
        """处理决策结果
       
        Args:
            decision: 决策结果字典
           
        Returns:
            成功时返回(True, 命令详情列表)，失败时返回(False, None)
        """
        if not isinstance(decision, dict):
            logger.error(f"决策结果格式错误: {decision}")
            return False, None
        
        logger.info(f"接收到大模型决策结果: {decision}")
        
        # 判断是否为顺序动作
        if decision.get("is_sequence", False) and "actions" in decision and isinstance(decision["actions"], list):
            logger.info("检测到顺序动作，启动序列执行")
            for action in decision["actions"]:
                if "angular_z" in action:
                    # 限制角速度的最大值
                    max_angular_z = 0.35  # 约20度
                    if abs(action["angular_z"]) > max_angular_z:
                        # 保持方向，但限制最大值
                        direction = 1 if action["angular_z"] > 0 else -1
                        action["angular_z"] = direction * max_angular_z
                        logger.info(f"已限制角速度为最大值: {action['angular_z']}")
            
            return self.execute_sequence(decision["actions"])
        
        # 兼容单个动作的情况（新格式）
        if "actions" in decision and isinstance(decision["actions"], list) and len(decision["actions"]) > 0:
            action = decision["actions"][0]
            # 提取速度参数
            duration = float(action.get("duration", 0))
            linear_x = float(action.get("linear_x", 0.0))
            linear_y = float(action.get("linear_y", 0.0))
            linear_z = float(action.get("linear_z", 0.0))
            angular_x = float(action.get("angular_x", 0.0))
            angular_y = float(action.get("angular_y", 0.0))
            angular_z = float(action.get("angular_z", 0.0))
            
            # 对阿克曼地盘进行角度限制（最大20度）
            max_angular_z = 0.35  # 约20度
            if abs(angular_z) > max_angular_z:
                # 保持方向，但限制最大值
                direction = 1 if angular_z > 0 else -1
                angular_z = direction * max_angular_z
                logger.info(f"已限制角速度为最大值: {angular_z}")
            
            logger.info(f"提取的速度参数 (新格式): linear_x={linear_x}, linear_y={linear_y}, linear_z={linear_z}, "
                      f"angular_x={angular_x}, angular_y={angular_y}, angular_z={angular_z}, duration={duration}")
        else:
            # 旧格式兼容性处理
            # 提取速度参数，如果不存在则使用默认值
            duration = float(decision.get("duration", 0))
            linear_x = float(decision.get("linear_x", 0.0))
            linear_y = float(decision.get("linear_y", 0.0))
            linear_z = float(decision.get("linear_z", 0.0))
            angular_x = float(decision.get("angular_x", 0.0))
            angular_y = float(decision.get("angular_y", 0.0))
            angular_z = float(decision.get("angular_z", 0.0))
            
            # 对阿克曼地盘进行角度限制（最大20度）
            max_angular_z = 0.35  # 约20度
            if abs(angular_z) > max_angular_z:
                # 保持方向，但限制最大值
                direction = 1 if angular_z > 0 else -1
                angular_z = direction * max_angular_z
                logger.info(f"已限制角速度为最大值: {angular_z}")
            
            logger.info(f"提取的速度参数 (旧格式): linear_x={linear_x}, linear_y={linear_y}, linear_z={linear_z}, "
                      f"angular_x={angular_x}, angular_y={angular_y}, angular_z={angular_z}, duration={duration}")
        
        # 发布速度命令
        if duration > 0:
            # 如果有持续时间，使用定时停止
            success, cmd_details = self.ros_bridge.publish_cmd_vel(
                linear_x=linear_x,
                linear_y=linear_y,
                linear_z=linear_z,
                angular_x=angular_x,
                angular_y=angular_y,
                angular_z=angular_z
            )
            
            if success:
                # 创建并启动定时停止线程
                self.timed_stop_thread = threading.Thread(
                    target=self._timed_stop_task,
                    args=(duration,)
                )
                self.timed_stop_thread.daemon = True
                self.timed_stop_event.clear()
                self.timed_stop_thread.start()
                
                # 添加短暂延时，确保线程有时间启动
                time.sleep(0.1)
                
                return True, [{
                    "command": decision.get("message", "执行速度命令"),
                    "details": cmd_details,
                    "duration": f"持续{duration}秒"
                }]
        else:
            # 如果没有持续时间，直接发送命令
            success, cmd_details = self.ros_bridge.publish_cmd_vel(
                linear_x=linear_x,
                linear_y=linear_y,
                linear_z=linear_z,
                angular_x=angular_x,
                angular_y=angular_y,
                angular_z=angular_z
            )
            
            if success:
                return True, [{
                    "command": decision.get("message", "执行速度命令"),
                    "details": cmd_details
                }]
        
        return False, None