import os
import click
import json
from audio_recognition import AudioRecognizer
from qwen_decision import QwenDecisionMaker
from ros_bridge import ROSBridgePublisher
from integrated_qt_interface import run_integrated_interface

# 全局参数设置
DEFAULT_ROS_BRIDGE_URL = "ws://192.168.195.173:9090"

def print_colored(text, color='green'):
    """打印彩色文本"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, colors['green'])}{text}{colors['end']}")

@click.group()
def cli():
    """语音识别与机器人控制决策系统"""
    pass

@cli.command()
@click.option('--ros-bridge-url', default=DEFAULT_ROS_BRIDGE_URL, help='ROSBridge WebSocket服务器URL')
def qt(ros_bridge_url):
    """启动Qt图形用户界面"""
    from qt_interface import run_qt_interface
    print_colored("正在启动Qt图形界面...", "blue")
    run_qt_interface(ros_bridge_url=ros_bridge_url)

@cli.command()
@click.option('--ros-bridge-url', default=DEFAULT_ROS_BRIDGE_URL, help='ROSBridge WebSocket服务器URL')
def qt2(ros_bridge_url):
    """启动集成Qt图形用户界面"""
    print_colored("正在启动集成Qt图形界面...", "blue")
    run_integrated_interface(ros_bridge_url=ros_bridge_url)

@cli.command()
@click.option('--duration', '-d', default=5, help='每次录音时长（秒）')
@click.option('--ros-bridge-url', default=DEFAULT_ROS_BRIDGE_URL, help='ROSBridge WebSocket服务器URL')
def run(duration, ros_bridge_url):
    """持续运行语音识别与机器人控制系统，直到按下Ctrl+C"""
    import signal
    import time
    import sys
    
    # 初始化模块
    print_colored("初始化语音识别模块...", "blue")
    recognizer = AudioRecognizer()
    
    print_colored("初始化决策分析模块...", "blue")
    decision_maker = QwenDecisionMaker()
    
    print_colored(f"初始化ROS桥接模块 (URL: {ros_bridge_url})...", "blue")
    ros_publisher = ROSBridgePublisher(ros_bridge_url=ros_bridge_url)
    
    # 连接到ROS桥接服务器
    if ros_publisher.connect():
        print_colored("已连接到ROSBridge服务器", "green")
    else:
        print_colored("无法连接到ROSBridge服务器，程序退出", "red")
        return
    
    # 定义信号处理函数，用于优雅退出
    def signal_handler(sig, frame):
        print_colored("\n接收到退出信号，正在停止系统...", "yellow")
        # 发送停止命令
        ros_publisher.publish_cmd_vel()
        # 断开ROS连接
        ros_publisher.disconnect()
        print_colored("已断开与ROSBridge服务器的连接", "green")
        print_colored("系统已安全退出", "blue")
        sys.exit(0)
    
    # 注册信号处理函数
    signal.signal(signal.SIGINT, signal_handler)
    
    print_colored("系统已启动，按Ctrl+C退出", "green")
    print_colored("开始持续监听语音命令...", "blue")
    
    try:
        while True:
            # 录制音频
            print_colored(f"\n请开始说话，录音将持续 {duration} 秒...", "yellow")
            audio_file = recognizer.record_audio(duration=duration)
            
            # 语音识别
            print_colored("正在进行语音识别...", "blue")
            text_result = recognizer.recognize_speech(audio_file)
            
            if not text_result:
                print_colored("未能识别到语音内容，请重试。", "red")
                continue
                
            print_colored(f"识别结果: {text_result}", "green")
            
            # 决策分析
            print_colored("正在进行决策分析...", "blue")
            decision_result = decision_maker.make_decision(text_result)
            
            # 输出决策结果
            print_colored("\n决策分析结果:", "purple")
            print(json.dumps(decision_result, ensure_ascii=False, indent=2))
            
            # 处理命令 (新格式)
            if decision_result.get("status") == "success":
                # 检查是否为顺序动作
                if decision_result.get("is_sequence", False) and "actions" in decision_result:
                    print_colored(f"\n机器人动作序列: {decision_result.get('message', '')}", "green")
                    print_colored(f"将按顺序执行以下动作:", "yellow")
                    
                    for i, action in enumerate(decision_result["actions"]):
                        description = action.get("description", f"动作 {i+1}")
                        duration = action.get("duration", 0)
                        linear_x = action.get("linear_x", 0.0)
                        linear_y = action.get("linear_y", 0.0)
                        linear_z = action.get("linear_z", 0.0)
                        angular_x = action.get("angular_x", 0.0)
                        angular_y = action.get("angular_y", 0.0)
                        angular_z = action.get("angular_z", 0.0)
                        
                        print_colored(f"\n动作 {i+1}: {description}", "blue")
                        print_colored(f"  持续时间: {duration} 秒", "yellow")
                        print_colored(f"  线速度 (x, y, z): ({linear_x}, {linear_y}, {linear_z}) m/s", "blue")
                        print_colored(f"  角速度 (x, y, z): ({angular_x}, {angular_y}, {angular_z}) rad/s", "blue")
                    
                    # 发布ROS命令序列
                    print_colored("\n正在发布ROS动作序列...", "blue")
                    ros_publisher.process_decision(decision_result)
                    print_colored("动作序列已开始执行", "green")
                    
                else:
                    # 兼容单个动作（既支持新格式也支持旧格式）
                    if "actions" in decision_result and isinstance(decision_result["actions"], list) and len(decision_result["actions"]) > 0:
                        # 新格式
                        action = decision_result["actions"][0]
                        duration = action.get("duration", 0)
                        linear_x = action.get("linear_x", 0.0)
                        linear_y = action.get("linear_y", 0.0)
                        linear_z = action.get("linear_z", 0.0)
                        angular_x = action.get("angular_x", 0.0)
                        angular_y = action.get("angular_y", 0.0)
                        angular_z = action.get("angular_z", 0.0)
                        description = action.get("description", "执行动作")
                        
                        message = decision_result.get("message", description)
                    else:
                        # 旧格式
                        duration = decision_result.get("duration", 0)
                        linear_x = decision_result.get("linear_x", 0.0)
                        linear_y = decision_result.get("linear_y", 0.0)
                        linear_z = decision_result.get("linear_z", 0.0)
                        angular_x = decision_result.get("angular_x", 0.0)
                        angular_y = decision_result.get("angular_y", 0.0)
                        angular_z = decision_result.get("angular_z", 0.0)
                        message = decision_result.get("message", "")

                    print_colored(f"\n机器人动作: {message}", "green")
                    print_colored(f"  持续时间: {duration} 秒", "yellow")
                    print_colored(f"  线速度 (x, y, z): ({linear_x}, {linear_y}, {linear_z}) m/s", "blue")
                    print_colored(f"  角速度 (x, y, z): ({angular_x}, {angular_y}, {angular_z}) rad/s", "blue")

                    # 发布ROS命令
                    print_colored("\n正在发布ROS命令...", "blue")
                    ros_publisher.publish_cmd_vel(
                        linear_x=linear_x,
                        linear_y=linear_y,
                        linear_z=linear_z,
                        angular_x=angular_x,
                        angular_y=angular_y,
                        angular_z=angular_z
                    )
                    print_colored("速度命令已发布", "green")

                # 等待指定时间
                if duration > 0:
                    print_colored(f"保持速度 {duration} 秒...", "yellow")
                    time.sleep(duration)
                    # 发送停止命令
                    print_colored("正在发送停止命令...", "blue")
                    ros_publisher.publish_cmd_vel() # 发送零速度
                    print_colored("停止命令已发送", "green")
                else:
                    # 如果持续时间为0，通常是停止命令，确保发送零速度
                    ros_publisher.publish_cmd_vel()

            elif decision_result.get("status") == "error":
                print_colored(f"决策分析出错: {decision_result.get('message')}", "red")
            else:
                print_colored("无法识别的决策结果格式", "red")
                print(decision_result)

            # 短暂暂停，避免过于频繁的循环
            time.sleep(0.5)
            
    except Exception as e:
        print_colored(f"执行过程中发生错误: {str(e)}", "red")
    finally:
        # 确保断开ROS连接
        ros_publisher.publish_cmd_vel()  # 发送停止命令
        ros_publisher.disconnect()
        print_colored("已断开与ROSBridge服务器的连接", "green")

if __name__ == "__main__":
    # 设置命令行颜色支持
    os.system('')  # 启用ANSI转义序列
    cli()