import os
import json
import time
import wave
import numpy as np
import pyaudio
import threading
import re
from openai import OpenAI
from dotenv import load_dotenv
import tempfile
import pyttsx3
from audio_recognition import AudioRecognizer

# 加载环境变量
load_dotenv()

class QwenDecisionMaker:
    def __init__(self):
        # 千问API配置
        self.api_key = "sk-4750fe61f4b14c78b6dfce21ccbbe287"
        self.api_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        
        if not self.api_key or not self.api_url:
            raise ValueError("请在.env文件中设置QWEN_API_KEY和QWEN_API_URL")
            
        # 初始化OpenAI客户端
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.api_url
        )
        
        # 音频配置
        self.format = pyaudio.paInt16
        self.channels = 1
        self.rate = 16000
        self.chunk = 1024
        self.record_seconds = 5
        
        # 初始化PyAudio
        try:
            self.audio = pyaudio.PyAudio()
            print("PyAudio已初始化成功")
        except Exception as e:
            print(f"PyAudio初始化失败: {str(e)}")
            self.audio = None
    
    def __del__(self):
        """析构函数，释放PyAudio资源"""
        if hasattr(self, 'audio') and self.audio:
            self.audio.terminate()
    
    def think_aloud(self, text_input, play_thinking=True):
        """展示大模型的思考过程
        
        Args:
            text_input: 需要分析的文本输入
            play_thinking: 是否播放完整思考过程，默认为True
            
        Returns:
            思考过程
        """
        try:
            completion = self.client.chat.completions.create(
                model="qwen-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": """你是一个机器人控制决策系统。
                        请详细展示你的思考过程，包括以下步骤：
                        1. 理解指令：分析用户指令的含义
                        2. 拆解任务：将指令拆解为可执行的子任务
                        3. 确定参数：明确每个子任务的具体参数（如方向、距离、秒数等）
                        4. 验证可行性：判断指令是否可以执行
                        5. 总结决策：最终转换为结构化命令
                        
                        特别注意：对于转向操作（左转、右转），请使用时间（秒）作为参数，而不是角度。"""
                    },
                    {
                        "role": "user",
                        "content": text_input
                    }
                ]
            )
            
            thinking_process = completion.choices[0].message.content
            print("思考过程:\n" + "-"*50)
            print(thinking_process)
            print("-"*50)
            
            # 只有当play_thinking为True时才播放思考过程
            if play_thinking:
                self.text_to_speech(thinking_process, is_command=False)
            
            return thinking_process
            
        except Exception as e:
            print(f"思考过程生成失败: {str(e)}")
            return f"思考过程生成失败: {str(e)}"
    
    def text_to_speech(self, text, is_command=True):
        """将文本转换为语音并播放
        
        Args:
            text: 需要播放的文本
            is_command: 是否是命令部分，默认为True，只有命令部分才会播放
        """
        if not self.audio:
            print(f"系统播报: {text} (PyAudio未初始化，无法播放语音)")
            return
            
        print(f"系统播报: {text}")
        
        # 只有当is_command为True时才实际播放语音
        if is_command:
            try:
                # 使用pyttsx3进行文本到语音转换
                engine = pyttsx3.init()
                engine.say(text)
                engine.runAndWait()
                print("(语音播放完成)")
            except Exception as e:
                print(f"语音播放失败: {str(e)}")
        else:
            print("(跳过思考过程的语音播放)")
    
    def record_audio(self, seconds=None):
        """使用PyAudio录制音频
        
        Args:
            seconds: 录制时长，如果为None则使用默认值
            
        Returns:
            录制的音频数据
        """
        if not self.audio:
            print("PyAudio未初始化，无法录制音频")
            return None
            
        seconds = seconds or self.record_seconds
        
        try:
            print("准备录制，请说话...")
            
            # 打开音频流
            stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.rate,
                input=True,
                frames_per_buffer=self.chunk
            )
            
            print("开始录音...")
            frames = []
            
            # 录制音频
            for i in range(0, int(self.rate / self.chunk * seconds)):
                data = stream.read(self.chunk)
                frames.append(data)
            
            print("录音结束")
            
            # 停止并关闭音频流
            stream.stop_stream()
            stream.close()
            
            return b''.join(frames)
            
        except Exception as e:
            print(f"录音过程中出错: {str(e)}")
            return None
    
    def speech_to_text(self, audio_data):
        """将音频数据转换为文本
        
        Args:
            audio_data: 录制的音频数据
            
        Returns:
            识别的文本
        """
        if not audio_data:
            return ""
            
        try:
            # 使用临时文件保存音频
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_audio:
                temp_filename = temp_audio.name
                
                # 写入WAV文件头和数据
                with wave.open(temp_filename, 'wb') as wf:
                    wf.setnchannels(self.channels)
                    wf.setsampwidth(self.audio.get_sample_size(self.format))
                    wf.setframerate(self.rate)
                    wf.writeframes(audio_data)
            
            # 使用AudioRecognizer进行语音识别
            recognizer = AudioRecognizer()
            recognized_text = recognizer.recognize_speech(temp_filename)
            
            if recognized_text:
                print(f"语音识别结果: {recognized_text}")
                return recognized_text
            else:
                print("语音识别失败，使用默认结果")
                return "向前移动两米"
            
        except Exception as e:
            print(f"语音识别失败: {str(e)}")
            return ""
        finally:
            # 删除临时文件
            if 'temp_filename' in locals():
                try:
                    os.remove(temp_filename)
                except:
                    pass
    
    def listen_command(self):
        """使用PyAudio录制音频并识别指令
        
        Returns:
            识别的文本命令
        """
        audio_data = self.record_audio()
        if audio_data:
            return self.speech_to_text(audio_data)
        return ""
    
    
    def make_decision(self, text_input, show_thinking=True, interactive=True):
        """使用千问API对输入文本进行决策分析，并根据需要进行交互式澄清
        
        Args:
            text_input: 需要分析的文本输入
            show_thinking: 是否显示思考过程
            interactive: 是否启用交互式思考
            
        Returns:
            决策结果字典
        """
        if not text_input.strip():
            return {"status": "error", "message": "输入文本为空", "command": ""}

        # 显示思考过程，但不播放思考过程的语音
        if show_thinking:
            thinking = self.think_aloud(text_input, play_thinking=False)
            # 在思考和决策之间添加短暂停顿，模拟思考时间
            time.sleep(1)

        current_instruction = text_input
        clarification_needed = True
        max_retries = 2 # 限制交互次数，防止无限循环
        retry_count = 0

        while clarification_needed and interactive and retry_count < max_retries:
            retry_count += 1
            try:
                # 调用LLM进行分析和决策
                completion = self.client.chat.completions.create(
                    model="qwen-turbo",
                    messages=[
                        {
                            "role": "system",
                            "content": """你是一个机器人控制决策系统。你需要分析用户的语音指令。
                            1. 判断指令是否清晰明确，足以转换为机器人动作。
                            2. 如果指令清晰，请生成机器人可执行的速度参数。
                            3. 如果指令模糊或缺少必要信息（如距离、时间），请提出具体的澄清问题。
                            4. 如果指令包含多个顺序动作，必须将其拆分为多个动作序列。
                            
                            特别注意：
                            - 必须积极识别顺序动作指令，包括但不限于含有以下表示顺序的词语或标志：
                              * "先...然后...", "先...再...", "...接着...", "...之后..."
                              * "第一步...", "第二步...", "首先...", "其次...", "最后..."
                              * 逗号或句号分隔的多个动作指令
                              * "并且", "同时", "接下来", "随后" 等表示连续动作的词语
                            - 当指令中包含多个动作时（如"小车直行2米，然后左转3秒"），必须将其解析为顺序动作序列，并设置is_sequence为true。
                            - 确保每个动作都有明确的持续时间和完整的参数，如果用户没有指定，则根据合理的速度和距离计算。
                            - 对于每个动作，都必须生成完整的速度参数集合，不能省略任何必要参数。
                            
                            请以JSON格式回复，包含以下字段：
                            { 
                                "is_clear": 布尔值 (true/false), 
                                "question": 字符串 (如果 is_clear 为 false，则包含澄清问题；否则为空字符串), 
                                "status": 字符串 ("success" 或 "clarification_needed"), 
                                "message": 字符串 (对指令的理解或回应),
                                "is_sequence": 布尔值 (true/false，指示是否为顺序动作),
                                "actions": [ // 如果是顺序动作，则包含多个动作，否则只包含一个动作
                                    {
                                        "description": 字符串 (对该动作的描述),
                                        "duration": 数字 (该动作持续时间),
                                        "linear_x": 数字 (X轴线速度),
                                        "linear_y": 数字 (Y轴线速度，通常为0),
                                        "linear_z": 数字 (Z轴线速度，通常为0),
                                        "angular_x": 数字 (X轴角速度，通常为0),
                                        "angular_y": 数字 (Y轴角速度，通常为0),
                                        "angular_z": 数字 (Z轴角速度)
                                    },
                                    // 可能有更多动作...
                                ]
                            }
                            
                            速度参数说明：
                            - linear_x: 前进为正值，后退为负值，通常在-1.0到1.0之间
                            - linear_y: 左右移动，通常为0
                            - linear_z: 上下移动，通常为0
                            - angular_x: 绕X轴旋转，通常为0
                            - angular_y: 绕Y轴旋转，通常为0
                            - angular_z: 绕Z轴旋转，左转为正值，右转为负值，通常在-1.0到1.0之间
                            - **重要：当生成左转或右转动作时，必须包含一个正向线速度 `linear_x`，建议值为 0.2 m/s，以实现带速度的转弯，而不是原地旋转。**
                            
                            顺序动作示例：
                            用户: "先前进1米，然后左转2秒"
                            回复: {
                                "is_clear": true, 
                                "question": "", 
                                "status": "success", 
                                "message": "我将先前进1米，然后左转2秒",
                                "is_sequence": true,
                                "actions": [
                                    {
                                        "description": "前进1米",
                                        "duration": 2, 
                                        "linear_x": 0.5, 
                                        "linear_y": 0.0, 
                                        "linear_z": 0.0,
                                        "angular_x": 0.0, 
                                        "angular_y": 0.0, 
                                        "angular_z": 0.0
                                    },
                                    {
                                        "description": "左转2秒",
                                        "duration": 2, 
                                        "linear_x": 0.2, 
                                        "linear_y": 0.0, 
                                        "linear_z": 0.0,
                                        "angular_x": 0.0, 
                                        "angular_y": 0.0, 
                                        "angular_z": 0.5
                                    }
                                ]
                            }

                            带线速度的右转示例：
                            用户: "向右转弯2秒"
                            回复: {
                                "is_clear": true,
                                "question": "",
                                "status": "success",
                                "message": "好的，正在右转2秒",
                                "is_sequence": false,
                                "actions": [
                                    {
                                        "description": "右转2秒",
                                        "duration": 2,
                                        "linear_x": 0.2,  # 注意这里增加了线速度
                                        "linear_y": 0.0,
                                        "linear_z": 0.0,
                                        "angular_x": 0.0,
                                        "angular_y": 0.0,
                                        "angular_z": -0.5 # 右转角速度为负
                                    }
                                ]
                            }
                            
                            单一动作示例：
                            用户: "向前走2米"
                            回复: {
                                "is_clear": true, 
                                "question": "", 
                                "status": "success", 
                                "message": "我将向前移动2米", 
                                "is_sequence": false,
                                "actions": [
                                    {
                                        "description": "前进2米",
                                        "duration": 4, 
                                        "linear_x": 0.5, 
                                        "linear_y": 0.0, 
                                        "linear_z": 0.0,
                                        "angular_x": 0.0, 
                                        "angular_y": 0.0, 
                                        "angular_z": 0.0
                                    }
                                ]
                            }
                            
                            模糊指令示例：
                            用户: "走几步"
                            回复: {
                                "is_clear": false, 
                                "question": "请问需要前进几米？", 
                                "status": "clarification_needed", 
                                "message": "指令不够明确",
                                "is_sequence": false,
                                "actions": []
                            }
                            """
                        },
                        {
                            "role": "user",
                            "content": current_instruction # 使用当前指令（可能包含之前的澄清）
                        }
                    ],
                    response_format={"type": "json_object"}
                )

                # 解析LLM响应
                try:
                    llm_response_str = completion.choices[0].message.content
                    decision_data = json.loads(llm_response_str)
                except (AttributeError, IndexError, json.JSONDecodeError, TypeError) as parse_error:
                    print(f"解析LLM响应失败: {parse_error}, 响应内容: {llm_response_str if 'llm_response_str' in locals() else '无法获取'}")
                    return {"status": "error", "message": "解析LLM响应失败", "command": "", "is_sequence": False, "actions": []}

                # 检查指令是否清晰
                if decision_data.get("is_clear"):
                    clarification_needed = False # 指令清晰，退出循环
                    # 语音播报决策结果
                    if 'message' in decision_data:
                        self.text_to_speech(decision_data['message'], is_command=True)
                    
                    # 确保返回的数据包含is_sequence和actions字段
                    if "is_sequence" not in decision_data:
                        decision_data["is_sequence"] = False
                    if "actions" not in decision_data or not decision_data["actions"]:
                        decision_data["actions"] = []
                    
                    # 添加兼容性逻辑，处理'duration'和'time'字段
                    if decision_data.get("actions"):
                        for action in decision_data["actions"]:
                            # 如果存在time字段但不存在duration字段，则使用time字段的值
                            if "time" in action and "duration" not in action:
                                action["duration"] = action["time"]
                                print(f"字段兼容处理: 将'time'字段值({action['time']})复制到'duration'字段")
                            if "duratio`" in action and "duration" not in action:
                                    action["duration"] = action["duratio`"]
                                    print(f"字段兼容处理:")
                            if "duratio n" in action and "duration" not in action:
                                    action["duration"] = action["duratio n"]
                                    print(f"字段兼容处理:")
                            # 确保duration字段存在且为数字
                            if "duration" not in action:
                                action["duration"] = 0
                                print("警告: 动作缺少持续时间字段，已设置为默认值0")
                            elif not isinstance(action["duration"], (int, float)) or action["duration"] < 0:
                                print(f"警告: 动作持续时间无效({action['duration']})，已设置为默认值0")
                                action["duration"] = 0
                        
                    # 如果是顺序动作，确保actions数组包含所有动作
                    if decision_data.get("is_sequence", False) and decision_data.get("actions"):
                        print(f"检测到顺序动作，共{len(decision_data['actions'])}个动作")
                        for i, action in enumerate(decision_data["actions"]):
                            print(f"动作 {i+1}: {action.get('description', '未命名动作')}")
                    
                    return decision_data # 返回最终决策
                else:
                    # 指令不清晰，需要交互
                    question = decision_data.get("question")
                    if question:
                        self.text_to_speech(question)
                        user_response = self.listen_command()
                        
                        if not user_response or user_response.strip() == "":
                            # 用户未回答或回答无效，可以考虑使用默认值或直接放弃
                            self.text_to_speech("未收到有效回答，将尝试使用原始指令进行决策。", is_command=True)
                            clarification_needed = False # 放弃交互，尝试用原始指令决策
                        else:
                            self.text_to_speech(f"好的，收到：{user_response}", is_command=True)
                            # 将用户回答附加到当前指令上，再次尝试
                            current_instruction = f"{current_instruction} (补充说明：{user_response})"
                            # 继续循环
                    else:
                        # LLM认为不清晰但没有提供问题，可能出错了
                        print("LLM认为指令不清晰，但未提供澄清问题。")
                        clarification_needed = False # 退出循环，尝试用原始指令决策

            except Exception as e:
                print(f"API请求或交互过程中出错: {str(e)}")
                return {"status": "error", "message": f"API请求或交互失败: {str(e)}", "command": "", "is_sequence": False, "actions": []}
        
        # 如果交互结束（达到最大次数或放弃）但仍未澄清，尝试最后一次决策
        if clarification_needed:
            print(f"交互达到最大次数({max_retries})或已放弃，尝试使用当前指令进行最终决策: {current_instruction}")
            # 进行最后一次尝试调用LLM
            try:
                completion = self.client.chat.completions.create(
                    model="qwen-turbo",
                    messages=[
                        {
                            "role": "system",
                            "content": """你是一个机器人控制决策系统。你需要分析用户的语音指令。
                            1. 判断指令是否清晰明确，足以转换为机器人动作。
                            2. 如果指令清晰，请生成机器人可执行的速度参数。
                            3. 如果指令模糊或缺少必要信息（如距离、时间），请提出具体的澄清问题。
                            4. 如果指令包含多个顺序动作，必须将其拆分为多个动作序列。
                            
                            特别注意：
                            - 必须积极识别顺序动作指令，包括但不限于含有以下表示顺序的词语或标志：
                              * "先...然后...", "先...再...", "...接着...", "...之后..."
                              * "第一步...", "第二步...", "首先...", "其次...", "最后..."
                              * 逗号或句号分隔的多个动作指令
                              * "并且", "同时", "接下来", "随后" 等表示连续动作的词语
                            - 当指令中包含多个动作时（如"小车直行2米，然后左转3秒"），必须将其解析为顺序动作序列，并设置is_sequence为true。
                            - 确保每个动作都有明确的持续时间和完整的参数，如果用户没有指定，则根据合理的速度和距离计算。
                            - 对于每个动作，都必须生成完整的速度参数集合，不能省略任何必要参数。
                            
                            请以JSON格式回复，包含以下字段：
                            { 
                                "is_clear": 布尔值 (true/false), 
                                "question": 字符串 (如果 is_clear 为 false，则包含澄清问题；否则为空字符串), 
                                "status": 字符串 ("success" 或 "clarification_needed"), 
                                "message": 字符串 (对指令的理解或回应),
                                "is_sequence": 布尔值 (true/false，指示是否为顺序动作),
                                "actions": [ // 如果是顺序动作，则包含多个动作，否则只包含一个动作
                                    {
                                        "description": 字符串 (对该动作的描述),
                                        "duration": 数字 (该动作持续时间),
                                        "linear_x": 数字 (X轴线速度),
                                        "linear_y": 数字 (Y轴线速度，通常为0),
                                        "linear_z": 数字 (Z轴线速度，通常为0),
                                        "angular_x": 数字 (X轴角速度，通常为0),
                                        "angular_y": 数字 (Y轴角速度，通常为0),
                                        "angular_z": 数字 (Z轴角速度)
                                    },
                                    // 可能有更多动作...
                                ]
                            }
                            
                            速度参数说明：
                            - linear_x: 前进为正值，后退为负值，通常在-1.0到1.0之间
                            - linear_y: 左右移动，通常为0
                            - linear_z: 上下移动，通常为0
                            - angular_x: 绕X轴旋转，通常为0
                            - angular_y: 绕Y轴旋转，通常为0
                            - angular_z: 绕Z轴旋转，左转为正值，右转为负值，通常在-1.0到1.0之间
                            - **重要：当生成左转或右转动作时，必须包含一个正向线速度 `linear_x`，建议值为 0.2 m/s，以实现带速度的转弯，而不是原地旋转。**
                            
                            顺序动作示例：
                            用户: "先前进1米，然后左转2秒"
                            回复: {
                                "is_clear": true, 
                                "question": "", 
                                "status": "success", 
                                "message": "我将先前进1米，然后左转2秒",
                                "is_sequence": true,
                                "actions": [
                                    {
                                        "description": "前进1米",
                                        "duration": 2, 
                                        "linear_x": 0.5, 
                                        "linear_y": 0.0, 
                                        "linear_z": 0.0,
                                        "angular_x": 0.0, 
                                        "angular_y": 0.0, 
                                        "angular_z": 0.0
                                    },
                                    {
                                        "description": "左转2秒",
                                        "duration": 2, 
                                        "linear_x": 0.2, 
                                        "linear_y": 0.0, 
                                        "linear_z": 0.0,
                                        "angular_x": 0.0, 
                                        "angular_y": 0.0, 
                                        "angular_z": 0.5
                                    }
                                ]
                            }

                            带线速度的右转示例：
                            用户: "向右转弯2秒"
                            回复: {
                                "is_clear": true,
                                "question": "",
                                "status": "success",
                                "message": "好的，正在右转2秒",
                                "is_sequence": false,
                                "actions": [
                                    {
                                        "description": "右转2秒",
                                        "duration": 2,
                                        "linear_x": 0.2,  # 注意这里增加了线速度
                                        "linear_y": 0.0,
                                        "linear_z": 0.0,
                                        "angular_x": 0.0,
                                        "angular_y": 0.0,
                                        "angular_z": -0.5 # 右转角速度为负
                                    }
                                ]
                            }
                            
                            单一动作示例：
                            用户: "向前走2米"
                            回复: {
                                "is_clear": true, 
                                "question": "", 
                                "status": "success", 
                                "message": "我将向前移动2米", 
                                "is_sequence": false,
                                "actions": [
                                    {
                                        "description": "前进2米",
                                        "duration": 4, 
                                        "linear_x": 0.5, 
                                        "linear_y": 0.0, 
                                        "linear_z": 0.0,
                                        "angular_x": 0.0, 
                                        "angular_y": 0.0, 
                                        "angular_z": 0.0
                                    }
                                ]
                            }
                            
                            模糊指令示例：
                            用户: "走几步"
                            回复: {
                                "is_clear": false, 
                                "question": "请问需要前进几米？", 
                                "status": "clarification_needed", 
                                "message": "指令不够明确",
                                "is_sequence": false,
                                "actions": []
                            }
                            """
                        },
                        {
                            "role": "user",
                            "content": current_instruction # 使用最终累积的指令
                        }
                    ],
                    response_format={"type": "json_object"}
                )
                
                # 解析最后一次尝试的响应
                try:
                    llm_response_str = completion.choices[0].message.content
                    final_decision_data = json.loads(llm_response_str)

                    # 检查最后一次尝试的结果是否清晰
                    if final_decision_data.get("is_clear"):
                        # 如果最终尝试清晰，则返回成功结果
                        if 'message' in final_decision_data:
                             self.text_to_speech(final_decision_data['message'], is_command=True)
                        
                        # 确保返回的数据包含is_sequence和actions字段
                        if "is_sequence" not in final_decision_data:
                            final_decision_data["is_sequence"] = False
                        if "actions" not in final_decision_data or not final_decision_data["actions"]:
                            final_decision_data["actions"] = []
                            
                        # 添加兼容性逻辑，处理'duration'和'time'字段
                        if final_decision_data.get("actions"):
                            for action in final_decision_data["actions"]:
                                # 如果存在time字段但不存在duration字段，则使用time字段的值
                                if "time" in action and "duration" not in action:
                                    action["duration"] = action["time"]
                                    print(f"字段兼容处理: 将'time'字段值({action['time']})复制到'duration'字段")
                                if "duratio`" in action and "duration" not in action:
                                    action["duration"] = action["duratio`"]
                                    print(f"字段兼容处理:")
                                if "duratio n" in action and "duration" not in action:
                                    action["duration"] = action["duratio n"]
                                    print(f"字段兼容处理:")
                                # 确保duration字段存在且为数字
                                if "duration" not in action:
                                    action["duration"] = 0
                                    print("警告: 动作缺少持续时间字段，已设置为默认值0")
                                elif not isinstance(action["duration"], (int, float)) or action["duration"] < 0:
                                    print(f"警告: 动作持续时间无效({action['duration']})，已设置为默认值0")
                                    action["duration"] = 0
                            
                        # 如果是顺序动作，确保actions数组包含所有动作
                        if final_decision_data.get("is_sequence", False) and final_decision_data.get("actions"):
                            print(f"检测到顺序动作，共{len(final_decision_data['actions'])}个动作")
                            for i, action in enumerate(final_decision_data["actions"]):
                                print(f"动作 {i+1}: {action.get('description', '未命名动作')}")
                        
                        return final_decision_data
                    else:
                        # 如果最终尝试仍然不清晰，则返回交互失败错误
                        print(f"最终尝试仍未获得清晰指令。LLM响应: {llm_response_str}")
                        # 保留原始错误信息，因为这确实是交互后仍无法确定的情况
                        # 可以考虑返回LLM最后一次的message，如果存在的话
                        error_message = final_decision_data.get("message", "无法通过交互获取清晰指令")
                        return {"status": "error", "message": error_message, "command": "", "is_sequence": False, "actions": []}

                except (AttributeError, IndexError, json.JSONDecodeError, TypeError) as parse_error:
                    print(f"解析最终LLM响应失败: {parse_error}, 响应内容: {llm_response_str if 'llm_response_str' in locals() else '无法获取'}")
                    return {"status": "error", "message": "解析最终LLM响应失败", "command": "", "is_sequence": False, "actions": []}

            except Exception as e:
                print(f"最终API请求失败: {str(e)}")
                return {"status": "error", "message": f"最终API请求失败: {str(e)}", "command": "", "is_sequence": False, "actions": []}
        
        # 如果循环正常结束（因为指令清晰），decision_data 已经包含结果
        # 此处代码理论上不应执行到，因为清晰时已 return
        # 但为防万一，保留一个错误返回
        return {"status": "error", "message": "决策流程异常结束", "command": "", "is_sequence": False, "actions": []}

# 测试代码
if __name__ == "__main__":
    decision_maker = QwenDecisionMaker()
    
    # # 测试1：基本决策功能
    # print("\n测试1：基本决策功能")
    # result = decision_maker.make_decision("向前移动两米", show_thinking=True, interactive=False)
    # print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 测试2：顺序动作指令
    # print("\n测试2：顺序动作指令")
    # result = decision_maker.make_decision("先前进2米，然后右转3秒", show_thinking=True, interactive=False)
    # print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 测试3：直接语音输入
    print("\n测试3：直接语音输入")
    print("请通过麦克风输入指令...")
    voice_command = decision_maker.listen_command()
    if voice_command:
        result = decision_maker.make_decision(voice_command, show_thinking=True, interactive=True)
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 打印可执行的机器人命令参数
        if result["status"] == "success" and "is_sequence" in result:
            if result["is_sequence"]:
                print(f"\n顺序执行的动作序列:")
                for i, action in enumerate(result["actions"]):
                    print(f"动作 {i+1}: {action['description']}")
                    print(f"  持续时间: {action['duration']}秒")
                    print(f"  线速度: [{action['linear_x']}, {action['linear_y']}, {action['linear_z']}]")
                    print(f"  角速度: [{action['angular_x']}, {action['angular_y']}, {action['angular_z']}]")
            else:
                # 兼容单个动作情况
                action = result["actions"][0] if result["actions"] else {}
                print(f"\n可执行的机器人命令参数:")
                print(f"持续时间: {action.get('duration', 0)}秒")
                print(f"线速度: [{action.get('linear_x', 0)}, {action.get('linear_y', 0)}, {action.get('linear_z', 0)}]")
                print(f"角速度: [{action.get('angular_x', 0)}, {action.get('angular_y', 0)}, {action.get('angular_z', 0)}]")